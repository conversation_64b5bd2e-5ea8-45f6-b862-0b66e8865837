variable "environment" {
  description = "The environment"
}

variable "aws_region" {
  description = "The AWS Region"
}

variable "fluent_bit_repository_url" {
  description = "FluentBit repository url"
}

variable "task_definition" {
  description = "Task Definition file"
}

variable "vpc_id" {
  description = "vpc_id"
}

variable "private_subnets" {
  description = "private_subnets"
}

variable "public_subnets" {
  description = "public_subnets"
}

variable "certificate_arn" {
  description = "certificate_arn"
}

variable "ecs_sqs_list_policy_resource" {
  description = "ecs_sqs_list_policy_resource"
}

variable "shedlock_table_arn" {
    description = "shedlock_table_arn"
}

variable "billpayment_table_arn" {
    description = "billpayment_table_arn"
}

variable "secrets_arns" {
  default = {}
  type    = map(string)
}

variable "first_run" {
  description = "first_run"
  type = bool
  default = false
}

variable "s3_bucket_arns" {
  description = "s3_bucket_arns"
  type = list(string)
}


variable "cognito_token_sns_topic_arn" {
  type = string
}

variable "cognito_custom_sender_kms_key_arn"{
  type = string
}