resource "aws_cognito_user_pool_domain" "main" {
  domain          = var.user_pool_domain_name
  certificate_arn = var.user_pool_domain_certificate_arn
  user_pool_id    = aws_cognito_user_pool.user_pool.id
}


resource "aws_cognito_user_pool" "user_pool" {
  name = var.user_pool_name

  auto_verified_attributes = ["email"]
  deletion_protection = "ACTIVE"

  schema {
    name                     = "email"
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    required                 = true
    string_attribute_constraints {
      min_length = 0
      max_length = 2048
    }
  }

  schema {
    name                     = "accountId"
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      min_length = 44
      max_length = 44
    }
  }

  password_policy {
    minimum_length                   = 8
    require_lowercase                = false
    require_numbers                  = true
    require_symbols                  = false
    require_uppercase                = false
    temporary_password_validity_days = 2
  }

  admin_create_user_config {
    allow_admin_create_user_only = true
  }

  mfa_configuration = "OPTIONAL"
  user_pool_add_ons {
    advanced_security_mode = "ENFORCED"
  }

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }

  sms_authentication_message = <<EOF
Seu codigo Friday: {####}. Nao compartilhe com ninguem.

@friday.ai #{####}
EOF

  verification_message_template {
    email_subject = "Friday - Recuperação de Senha"
    email_message = "Seu código para recuperação de senha é {####}. Não compartilhe com ninguém.<br><br>Acesse https://use.friday.ai/esqueci-minha-senha e digite o código acima."
    sms_message   = "Friday - Seu código para recuperação de senha é {####}. Não compartilhe com ninguém."
  }

  sms_configuration {
    external_id    = "1d858b1a-d793-4ff3-9fd8-889342b08cdf"
    sns_caller_arn = aws_iam_role.user_pool_SMS_role.arn
  }

  email_configuration {
    email_sending_account = "DEVELOPER"
    source_arn            = var.notification_email_sender_arn
  }

  device_configuration {
    device_only_remembered_on_user_prompt = false
    challenge_required_on_new_device      = true
  }

  dynamic "lambda_config" {
    for_each = var.lambda_config_enabled ? [1] : []
    content {
      custom_sms_sender {
        lambda_arn     = module.token_sender.arn
        lambda_version = "V1_0"
      }
      kms_key_id = aws_kms_key.cognito_custom_sender.arn
    }
  }
}

resource "aws_iam_role" "group_role" {
  name = "user-group-role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Federated": "cognito-identity.amazonaws.com"
      },
      "Action": "sts:AssumeRoleWithWebIdentity",
      "Condition": {
        "StringEquals": {
          "cognito-identity.amazonaws.com:aud": "us-east-1:********-dead-beef-cafe-123456790ab",
          "sts:ExternalId": "1d858b1a-d793-4ff3-9fd8-889342b08cdf"
        },
        "ForAnyValue:StringLike": {
          "cognito-identity.amazonaws.com:amr": "authenticated"
        }
      }
    }
  ]
}
EOF
}

resource "aws_iam_role" "user_pool_SMS_role" {
  name               = "user_pool_SMS_role"
  assume_role_policy = data.aws_iam_policy_document.instance-assume-role-policy.json
}

data "aws_iam_policy_document" "instance-assume-role-policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type = "Service"
      identifiers = ["cognito-idp.amazonaws.com"]
    }
  }
}

resource "aws_iam_policy" "user_pool_sms_policy" {
  name        = "test-policy"
  description = "A test policy"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "sns:publish"
      ],
      "Resource": [
        "*"
      ]
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "test-attach" {
  role       = aws_iam_role.user_pool_SMS_role.name
  policy_arn = aws_iam_policy.user_pool_sms_policy.arn
}

resource "aws_cognito_user_group" "group_owner" {
  name         = "OWNER"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  description  = "Managed by Terraform"
  precedence   = 0
  role_arn     = aws_iam_role.group_role.arn
}

resource "aws_cognito_user_group" "group_assistant" {
  name         = "ASSISTANT"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  description  = "Managed by Terraform"
  precedence   = 1
  role_arn     = aws_iam_role.group_role.arn
}

resource "aws_cognito_user_group" "group_guest" {
  name         = "GUEST"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  description  = "Managed by Terraform"
  precedence   = 2
  role_arn     = aws_iam_role.group_role.arn
}

resource "aws_cognito_user_pool_client" "client" {
  name                                 = "client"
  user_pool_id                         = aws_cognito_user_pool.user_pool.id
  explicit_auth_flows = ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH"]
  prevent_user_existence_errors        = "ENABLED"
  access_token_validity                = var.access_token_validity
  id_token_validity                    = var.id_token_validity
  allowed_oauth_scopes = ["email", "openid", "profile"]
  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_flows = ["code"]
  callback_urls                        = var.callback_urls
  logout_urls                          = var.logout_urls
  supported_identity_providers = ["COGNITO"]
  token_validity_units {
    access_token  = "minutes"
    id_token      = "minutes"
    refresh_token = "days"
  }

}


// Tópico SNS para tokens do Cognito
resource "aws_sns_topic" "cognito_token" {
  name = "${var.user_pool_name}-cognito-token"

  tags = {
    Environment = var.environment
  }
}

data "aws_secretsmanager_secret" "datadog_key" {
  arn = var.datadog_key_arn
}

data "aws_secretsmanager_secret_version" "datadog_key_version" {
  secret_id = data.aws_secretsmanager_secret.datadog_key.id
}

locals {
  secret_json = jsondecode(data.aws_secretsmanager_secret_version.datadog_key_version.secret_string)
  datadog_api_key = local.secret_json["DD_API_KEY"]
}

// Lambda function para custom sender do Cognito
module "token_sender" {
  source  = "DataDog/lambda-datadog/aws"
  version = "3.0.0"

  filename      = data.archive_file.lambda_zip.output_path
  function_name = "${var.prefix}-cognito-token-sender"
  role          = aws_iam_role.lambda_token_role.arn
  handler       = "index.handler"
  runtime       = "nodejs20.x"
  timeout       = 30
  memory_size   = 128

  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  datadog_extension_layer_version = 74
  datadog_node_layer_version      = 123

  environment_variables = {
    "USE_HTTP_DELIVERY": false
    "TENANT_ID" : "FRIDAY"
    "TOPIC_ARN" : aws_sns_topic.cognito_token.arn
    "DATADOG_API_KEY" : local.datadog_api_key
    "DD_ENV" : var.environment
    "DD_SERVICE" : "${var.prefix}-cognito-token-sender"
    "DD_SITE" : "datadoghq.com"
    "DD_VERSION" : "1.0.0"
    # Minimize cold start duration (https://docs.datadoghq.com/serverless/aws_lambda/installation/nodejs/?tab=terraform#minimize-cold-start-duration)
    "DD_TRACE_OTEL_ENABLED" : "false"
    "DD_PROFILING_ENABLED" : "false"
    "DD_SERVERLESS_APPSEC_ENABLED" : "false"
  }
}


locals {
  lambda_hash = filebase64sha256("${path.module}/lambda/token_sender/index.js")
}

// Arquivo ZIP para a Lambda
data "archive_file" "lambda_zip" {
  type        = "zip"
  output_path = "${path.module}/lambda/token_sender_${local.lambda_hash}.zip"
  source_file = "${path.module}/lambda/token_sender/index.js"
}

// IAM role para a Lambda
resource "aws_iam_role" "lambda_token_role" {
  name = "${var.user_pool_name}-token-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

// Política para a Lambda acessar CloudWatch Logs e SNS
resource "aws_iam_role_policy" "lambda_sns_policy" {
  name = "${var.user_pool_name}-lambda-sns-policy"
  role = aws_iam_role.lambda_token_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = [
          aws_sns_topic.cognito_token.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = [
          aws_kms_key.cognito_custom_sender.arn
        ]
      }
    ]
  })
}

// Permissão para o Cognito invocar a Lambda
resource "aws_lambda_permission" "cognito_token" {
  statement_id  = "AllowCognitoInvoke"
  action        = "lambda:InvokeFunction"
  function_name = module.token_sender.function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.user_pool.arn
}

// KMS key para criptografia do custom sender
resource "aws_kms_key" "cognito_custom_sender" {
  description             = "KMS key for Cognito custom sender"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = {
    Environment = var.environment
  }
}

resource "aws_kms_alias" "cognito_custom_sender" {
  name          = "alias/${var.user_pool_name}-custom-sender"
  target_key_id = aws_kms_key.cognito_custom_sender.key_id
}
