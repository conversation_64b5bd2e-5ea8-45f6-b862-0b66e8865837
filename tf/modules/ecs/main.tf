resource "aws_ecr_repository" "default" {
  image_tag_mutability = "IMMUTABLE"
  name                 = var.ecr_repository_name
}

resource "aws_ecr_repository" "default_2" {
  image_tag_mutability = "IMMUTABLE"
  name                 = var.ecr_repository_name_2
}

module "execution_role" {
  source = "./modules/execution-role"

  ecs_task_execution_role_name = var.ecs_task_execution_role_name
  secrets_enabled              = var.secrets_enabled
  secrets_arns                 = var.secrets_arns
  task_name                    = var.task_name
  task_name_2                  = var.task_name_2
  aws_region                   = var.aws_region
}

module "task_role" {
  source = "./modules/task-role"

  task_name                  = var.task_name
  task_name_2                = var.task_name_2
  aws_region                 = var.aws_region
  ecs_dynamo_policy_resource = var.ecs_dynamo_policy_resource
  ecs_task_role_name         = var.ecs_task_role_name
  dynamo_access_enabled      = var.dynamo_access_enabled
  lambda_access_enabled      = var.lambda_access_enabled
  ecs_lambda_policy_resource = var.ecs_lambda_policy_resource
  sqs_access_enabled         = var.sqs_access_enabled
  ecs_sqs_policy_resource    = var.ecs_sqs_policy_resource
  ecs_sns_policy_resource    = var.ecs_sns_policy_resource
  sns_access_enabled         = var.sns_access_enabled
  s3_bucket_arns             = var.s3_bucket_arns
  s3_read_objects            = var.s3_read_objects
  textract_enabled           = var.textract_enabled
  send_email                 = var.send_email
  user_pool_arn              = var.user_pool_arn
}

resource "aws_cloudwatch_log_group" "default" {
  name = "/ecs/${var.task_name}"
}

resource "aws_cloudwatch_log_group" "limited" {
  name = "/logs/${var.task_name}"
  retention_in_days = 30
}

# ALB Security group
# This is the group you need to edit if you want to restrict access to your application
resource "aws_security_group" "lb" {
  name        = "group-ecs-alb"
  description = "controls access to the ALB"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 8080
    to_port     = 8080
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"] # add a CIDR block here
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Traffic to the ECS Cluster should only come from the ALB
resource "aws_security_group" "ecs_tasks" {
  name        = "group-ecs-tasks"
  description = "allow inbound access from the ALB only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = var.app_port
    to_port         = var.app_port
    security_groups = [aws_security_group.lb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_alb" "main" {
  name            = var.load_balance_name
  subnets         = var.aws_public_subnet_id
  security_groups = [aws_security_group.lb.id]
  access_logs {
    bucket  = var.access_logs_bucket
    enabled = var.access_logs_enabled
  }

}

resource "aws_alb_target_group" "app" {
  name                 = var.alb_target_group_name
  port                 = var.app_port
  protocol             = "HTTPS"
  vpc_id               = var.aws_vpc_id
  target_type          = "ip"
  deregistration_delay = 120

  health_check {
    healthy_threshold   = "3"
    unhealthy_threshold = "6"
    interval            = "60"
    protocol            = "HTTPS"
    matcher             = "200-499"
    timeout             = "50"
    path                = var.health_check_path
  }
}

# Redirect all traffic from the ALB to the target group
resource "aws_alb_listener" "front_end" {
  load_balancer_arn = aws_alb.main.id
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = var.certificate_arn
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  default_action {
    target_group_arn = aws_alb_target_group.app.id
    type             = "forward"
  }
}

resource "aws_lb_listener_certificate" "friday_certificate" {
  count = var.alternative_certificate_arn_enabled  ? 1 : 0
  listener_arn    = aws_alb_listener.front_end.arn
  certificate_arn = var.alternative_certificate_arn
}

resource "aws_ecs_cluster" "main" {
  name = var.cluster_name
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecs_task_definition" "app" {
  family                   = var.task_name
  execution_role_arn       = module.execution_role.arn
  task_role_arn            = module.task_role.arn
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.fargate_cpu
  memory                   = var.fargate_memory
  container_definitions    = templatefile(var.task_definition, {
    app_version = var.app_version
    secrets_map = var.secrets_map
  })
  tags = {

  }
}

resource "aws_ecs_task_definition" "app_2" {
  family                   = var.task_name_2
  execution_role_arn       = module.execution_role.arn
  task_role_arn            = module.task_role.arn
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.fargate_cpu
  memory                   = var.fargate_memory
  container_definitions    = templatefile(var.task_definition_2, {
    app_version = var.app_version_2
  })
  tags = {

  }
}

resource "aws_ecs_service" "main" {
  name             = var.service_name
  cluster          = aws_ecs_cluster.main.id
  task_definition  = aws_ecs_task_definition.app.arn
  desired_count    = var.app_count
  launch_type      = "FARGATE"
  platform_version = "1.4.0"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = var.aws_private_subnet_id
    assign_public_ip = true
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.app.id
    container_name   = "BillPaymentAPI"
    container_port   = var.app_port
  }

  depends_on = [aws_alb_listener.front_end, module.execution_role.role_policy_attachment_execution_role]
}

#resource "aws_appautoscaling_target" "main_ecs_target" {
#  max_capacity       = 5
#  min_capacity       = 1
#  resource_id        = "service/${aws_ecs_cluster.main.name}/${aws_ecs_service.main.name}"
#  scalable_dimension = "ecs:service:DesiredCount"
#  service_namespace  = "ecs"
#  role_arn           = "arn:aws:iam::402556871325:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService"
#}
#
#resource "aws_appautoscaling_policy" "main_ecs_policy" {
#  name               = "FargateCPUScaling"
#  policy_type        = "TargetTrackingScaling"
#  resource_id        = aws_appautoscaling_target.main_ecs_target.resource_id
#  scalable_dimension = aws_appautoscaling_target.main_ecs_target.scalable_dimension
#  service_namespace  = aws_appautoscaling_target.main_ecs_target.service_namespace
#
#  target_tracking_scaling_policy_configuration {
#    scale_in_cooldown  = 300
#    scale_out_cooldown = 300
#predefined_metric_specification {
#      predefined_metric_type = "ECSServiceAverageCPUUtilization"
#    }
#
#    target_value = 70
#  }
#}

resource "aws_ecs_service" "main_2" {
  name             = var.service_name_2
  cluster          = aws_ecs_cluster.main.id
  task_definition  = aws_ecs_task_definition.app_2.arn
  desired_count    = var.app_count_2
  launch_type      = "FARGATE"
  platform_version = "1.4.0"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = var.aws_private_subnet_id
    assign_public_ip = true
  }

#  load_balancer {
#    target_group_arn = aws_alb_target_group.app_2.id
#    container_name   = "DDABills"
#    container_port   = var.app_port
#  }

  depends_on = [module.execution_role.role_policy_attachment_execution_role]
}

resource "aws_cloudwatch_log_group" "app_2" {
  name = "/ecs/dda-bills-task"
}

/*resource "aws_appautoscaling_target" "target" {
  service_namespace  = "ecs"
  resource_id        = "service/${aws_ecs_cluster.main.name}/${aws_ecs_service.main.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  min_capacity       = var.app_count
  max_capacity       = var.app_count
}*/

module "bill_events_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_events_dlq"

  message_retention_seconds = 1209600
}
