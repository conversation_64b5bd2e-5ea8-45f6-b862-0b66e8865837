# ALB Security group
# This is the group you need to edit if you want to restrict access to your application
resource "aws_security_group" "lb" {
  name        = (var.prefix == "bill-payment" ? "group-ecs-alb" : "${var.prefix}-security-group-alb")
  description = "controls access to the ALB"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 8080
    to_port     = 8080
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"]
  }

  dynamic "ingress" {
    for_each = var.mtls_enabled ? [1] : []
    content {
      protocol    = "tcp"
      from_port   = 8443
      to_port     = 8443
      cidr_blocks = ["0.0.0.0/0"]
    }
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Traffic to the ECS Cluster should only come from the ALB
resource "aws_security_group" "ecs_tasks" {
  #name        = "${var.prefix}-security-group-tasks"
  name        = (var.prefix == "bill-payment" ? "group-ecs-tasks" : "${var.prefix}-security-group-tasks")

  description = "allow inbound access from the ALB only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = var.app_port
    to_port         = var.app_port
    security_groups = [aws_security_group.lb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_alb" "main" {
  count           = var.load_balance_enabled ? 1 : 0
  name            = "${var.prefix}-alb"
  subnets         = var.aws_public_subnet_id
  security_groups = [aws_security_group.lb.id]
  idle_timeout = var.alb_idle_timeout

  access_logs {
    bucket  = var.access_logs_bucket
    enabled = var.access_logs_enabled
  }
}

resource "aws_alb_target_group" "app" {
  count                = var.load_balance_enabled ? 1 : 0
  name                 = "${var.prefix}-target-group"
  port                 = var.app_port
  protocol             = var.target_group_protocol
  vpc_id               = var.aws_vpc_id
  target_type          = "ip"
  deregistration_delay = 120

  health_check {
    healthy_threshold   = "3"
    unhealthy_threshold = "6"
    interval            = "60"
    protocol            = var.target_group_protocol
    matcher             = "200-499"
    timeout             = "50"
    path                = var.health_check_path
  }
}

# Redirect all traffic from the ALB to the target group
resource "aws_alb_listener" "front_end" {
  count             = var.load_balance_enabled ? 1 : 0
  load_balancer_arn = aws_alb.main[0].id
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = var.certificate_arn_enabled == true ? var.certificate_arn : null
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"

  default_action {
    target_group_arn = aws_alb_target_group.app[0].id
    type             = "forward"
  }
}

resource "aws_alb_listener" "mtls_front_end" {
  count             = var.load_balance_enabled && var.mtls_enabled ? 1 : 0
  load_balancer_arn = aws_alb.main[0].id
  port              = 8443
  protocol          = "HTTPS"
  certificate_arn   = var.certificate_arn_enabled == true ? var.certificate_arn : null
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"

  mutual_authentication {
    mode            = "passthrough"
    # trust_store_arn = aws_lb_trust_store.mtls[0].arn  # couldn't use with passthrough mode
  }

  default_action {
    target_group_arn = aws_alb_target_group.app[0].id
    type             = "forward"
  }
}

resource "aws_lb_listener_rule" "mtls_paths" {
  count        = var.load_balance_enabled && var.mtls_enabled && length(var.mtls_paths) > 0 ? 1 : 0
  listener_arn = aws_alb_listener.mtls_front_end[0].arn
  priority     = 1

  action {
    type             = "forward"
    target_group_arn = aws_alb_target_group.app[0].id
  }

  condition {
    path_pattern {
      values = var.mtls_paths
    }
  }
}

resource "aws_lb_listener_rule" "non_mtls_paths" {
  count        = var.load_balance_enabled && var.mtls_enabled && length(var.mtls_paths) > 0 ? 1 : 0
  listener_arn = aws_alb_listener.front_end[0].arn
  priority     = 1

  action {
    type             = "forward"
    target_group_arn = aws_alb_target_group.app[0].id
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }
}

resource "aws_lb_trust_store" "mtls" {
  count = var.mtls_enabled ? 1 : 0

  name = "${var.prefix}-mtls-trust-store"

  ca_certificates_bundle_s3_bucket = var.mtls_certificate_bucket
  ca_certificates_bundle_s3_key    = var.mtls_certificate_key

  lifecycle {
    precondition {
      condition     = var.mtls_enabled ? var.mtls_certificate_bucket != "" : true
      error_message = "mtls_certificate_bucket must be set when mtls_enabled is true"
    }
    precondition {
      condition     = var.mtls_enabled ? var.mtls_certificate_key != "" : true
      error_message = "mtls_certificate_key must be set when mtls_enabled is true"
    }
  }
}

resource "aws_lb_listener_certificate" "friday_certificate" {
  count = var.alternative_certificate_arn_enabled  ? 1 : 0
  listener_arn    = aws_alb_listener.front_end[0].arn
  certificate_arn = var.alternative_certificate_arn
}

resource "aws_ecs_service" "main" {
  name             = "${var.prefix}-service"
  cluster          = var.aws_ecs_cluster.id
  task_definition  = var.task_definition.arn
  desired_count    = var.app_count
  launch_type      = "FARGATE"
  platform_version = "1.4.0"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = var.aws_private_subnet_id
    assign_public_ip = true
  }

  dynamic load_balancer {
    for_each         = aws_alb_target_group.app
    content {
      target_group_arn = load_balancer.value.id
      container_name   = var.container_name
      container_port   = var.app_port
    }
  }

  depends_on = [aws_alb_listener.front_end]
}
