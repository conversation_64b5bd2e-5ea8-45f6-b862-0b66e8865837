locals {
  task_name               = "${var.prefix}-task"
  ecs_execution_role_name = "${var.prefix}-execution-role"
  ecs_task_role_name      = "${var.prefix}-task-role"
}

resource "aws_ecr_repository" "default" {
  image_tag_mutability = "IMMUTABLE"
  name                 = var.ecr_repository_name
}

module "execution_role" {
  source = "./modules/execution-role"

  ecs_task_execution_role_name = local.ecs_execution_role_name
  secrets_enabled              = var.secrets_enabled
  secrets_arns                 = var.secrets_arns
  task_name                    = local.task_name
  aws_region                   = var.aws_region
}

module "task_role" {
  source = "./modules/task-role"

  task_name                   = local.task_name
  aws_region                  = var.aws_region
  ecs_dynamo_policy_resource  = var.ecs_dynamo_policy_resource
  ecs_task_role_name          = local.ecs_task_role_name
  dynamo_access_enabled       = var.dynamo_access_enabled
  lambda_access_enabled       = var.lambda_access_enabled
  ecs_lambda_policy_resource  = var.ecs_lambda_policy_resource
  sqs_access_enabled          = var.sqs_access_enabled
  ecs_sqs_policy_resource     = var.ecs_sqs_policy_resource
  ecs_sns_policy_resource     = var.ecs_sns_policy_resource
  sns_access_enabled          = var.sns_access_enabled
  s3_bucket_arns              = var.s3_bucket_arns
  s3_read_objects             = var.s3_read_objects
  textract_enabled            = var.textract_enabled
  send_email                  = var.send_email
  user_pool_arn_enabled       = var.user_pool_arn_enabled
  user_pool_arn               = var.user_pool_arn
  kms_enabled                 = var.kms_enabled
  kms_key_arns                = var.kms_key_arns
  transcribe_audio_enabled    = var.transcribe_audio_enabled
  cloudwatch_log_read_enabled = var.cloudwatch_log_read_enabled
}

resource "aws_cloudwatch_log_group" "default" {
  name = "/ecs/${local.task_name}"
}

resource "aws_cloudwatch_log_group" "datadog" {
  name = "/ecs/${local.task_name}-dd"
}

resource "aws_cloudwatch_log_group" "limited" {
  name              = "/logs/${local.task_name}"
  retention_in_days = 30
}

resource "aws_ecs_task_definition" "app" {
  family             = local.task_name
  execution_role_arn = module.execution_role.arn
  task_role_arn      = module.task_role.arn
  network_mode       = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                = var.fargate_cpu
  memory             = var.fargate_memory

  dynamic "ephemeral_storage" {
    for_each = var.ephemeral_storage > 0 ? [1] : []
    content {
      size_in_gib = var.ephemeral_storage
    }
  }

  container_definitions = templatefile(var.task_definition, merge({
    app_version               = var.app_version
    app_version_2             = var.app_version_2
    app_version_3             = var.app_version_3
    app_version_4             = var.app_version_4
    fluent_bit_repository_url = var.fluent_bit_repository_url
    secrets_map               = var.secrets_map

  }, var.aditional_container_definitions))
  tags = {

  }
}
