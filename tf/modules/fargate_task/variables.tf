variable "ecr_repository_name" {
  description = "ECR repository to store our Docker images"
}

variable "prefix" {
  description = "Prefix to use on ALB, target group and service names"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "app_version" {
  description = "aplication version/container id"
}

variable "app_version_2" {
  description = "second aplication version/container id"
  default     = ""
}

variable "app_version_3" {
  description = "third aplication version/container id"
  default     = ""
}

variable "app_version_4" {
  description = "third aplication version/container id"
  default     = ""
}

variable "fargate_cpu" {
  description = "Fargate instance CPU units to provision (1 vCPU = 1024 CPU units)"
  default     = "256"
}

variable "ephemeral_storage" {
  description = "Fargate ephemeral storage"
  default     = 21
}

variable "fargate_memory" {
  description = "Fargate instance memory to provision (in MiB)"
  default     = "512"
}

variable "task_definition" {
  description = "path to task definition json"
}

variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "ecs_dynamo_policy_resource" {
  description = " ECS dynamo policy resources"
}

variable "ecs_lambda_policy_resource" {
  description = "ECS policy resources for lambda integration"
  default     = []
}

variable "lambda_access_enabled" {
  default = false
}

variable "secrets_enabled" {
  default = false
}

variable "secrets_arns" {
  default = []
  type    = list(string)
}

variable "ecs_sqs_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default     = []
}

variable "sqs_access_enabled" {
  default = false
}

variable "ecs_sns_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default     = []
}

variable "sns_access_enabled" {
  default = false
}

variable "s3_bucket_arns" {
  type    = list(string)
  default = []
}
variable "s3_read_objects" {
  default = false
}

variable "textract_enabled" {
  default = false
}

/*
  Usage: "ENVIRONMENT_VARIABLE_NAME" = "SECRET_VALUE"
  {
    "INTEGRATIONS_CLEARSALE_HOST" = "${aws_secretsmanager_secret.via1_clearsale-credentials.arn}:HOST::"
  }

  Check secrets_mapping.tf for more samples
*/
variable "secrets_map" {
  type = map(string)
  default = {}
}

variable "send_email" {
  description = "Permission to send emails"
  default     = false
}

variable "user_pool_arn_enabled" {
  default = true
}

variable "user_pool_arn" {
  default = ""
}

variable "kms_enabled" {
  default = false
}

variable "kms_key_arns" {
  default = []
}

variable "fluent_bit_repository_url" {
  default = ""
}

variable "aditional_container_definitions" {
  type = map(string)
  default = {}
}

variable "transcribe_audio_enabled" {
  type = bool
  default = false
}

variable "cloudwatch_log_read_enabled" {
  type = bool
  default = false
}
