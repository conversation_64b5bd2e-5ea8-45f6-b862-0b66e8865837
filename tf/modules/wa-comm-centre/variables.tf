variable "environment" {
  description = "The environment"
}

variable "aws_region" {
  description = "The AWS Region"
}

variable "fluent_bit_repository_url" {
  description = "FluentBit repository url"
}

variable "task_definition" {
  description = "Task Definition file"
}

variable "vpc_id" {
  description = "vpc_id"
}

variable "private_subnets" {
  description = "private_subnets"
}

variable "public_subnets" {
  description = "public_subnets"
}

variable "certificate_arn" {
  description = "certificate_arn"
}

variable "ecs_sqs_policy_resource" {}

variable "wa_comm_centre_bucket_name"{
  type = string
}

variable "mtls_certificate_bucket" {
  description = "S3 bucket containing the mTLS certificate"
  type        = string
}

variable "mtls_certificate_key" {
  description = "S3 key of the mTLS certificate"
  type        = string
}

variable "me_poupe_cross_account_role" {
  description = "me-poupe account role to access wa-comm-centre S3 bucket"
  type        = string
  default     = ""
}

variable "shedlock_table_arn" {
    description = "shedlock_table_arn"
}