[{"name": "DDABills", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/dda-bills:${app_version}", "cpu": 1995, "memory": 3840, "essential": true, "secrets": [{"name": "TENANTS_MOTOROLA_CONFIGURATION_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:MOTOROLA_CALLBACK_SECRET::"}, {"name": "TENANTS_GIGU_CONFIGURATION_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:GIGU_CALLBACK_SECRET::"}], "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "production,prodfriday,friday"}, {"name": "JAVA_OPTS", "value": "-Xmx3072m"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "dda-service"}], "dockerLabels": {"com.datadoghq.tags.env": "production", "com.datadoghq.tags.service": "dda-service"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "us-east-1", "log_group_name": "/ecs/dda-bills-task", "auto_create_group": "false", "log_stream_name": "ecs/dda-bills-task/$(ecs_task_id)"}}}, {"name": "datadog-agent", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1", "cpu": 51, "memory": 256, "essential": true, "environment": [{"name": "DD_API_KEY", "value": "********************************"}, {"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "portMappings": [{"containerPort": 8125, "hostPort": 8125}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dda-bills-task", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}, {"image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/custom-fluent-bit:2.3", "name": "log_router", "essential": true, "cpu": 0, "portMappings": [], "user": "0", "volumesFrom": [], "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}, "environment": [{"name": "SERVICE_CONTAINER_NAME", "value": "DDAService"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "dda-service"}, {"name": "LOG_GROUP_NAME", "value": "/logs/dda-bills-task"}, {"name": "REGION", "value": "us-east-1"}, {"name": "DD_API_KEY", "value": "********************************"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dda-bills-task", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "memoryReservation": 50}]