[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "cpu": 502,
    "memory": 1792,
    "essential": true,
    "stopTimeout": 120,
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "friday,motorola,gigu,${environment}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "secrets": [
      {
        "name": "CELCOIN_CALLBACK_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:CELCOIN_SECRET::"
      },
      {
        "name": "ARBI_CALLBACK_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:ARBI_SECRET::"
      },
      {
        "name": "INTEGRATIONS_CELCOIN_SSL_PASS",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:MTLS_P12_PASSWORD::"
      },
      {
        "name": "SETTLEMENT_CLIENT_FRIDAY_PROVIDER_METADATA_ARBI_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:FRIDAY_CLIENT_SECRET::"
      },
      {
        "name": "SETTLEMENT_CLIENT_FRIDAY_PROVIDER_METADATA_ARBI_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:FRIDAY_CLIENT_ID::"
      },
      {
        "name": "SETTLEMENT_CLIENT_FRIDAY_PROVIDER_METADATA_ARBI_USER_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:FRIDAY_USER_TOKEN::"
      },
      {
        "name": "SETTLEMENT_CLIENT_MOTOROLA_PROVIDER_METADATA_ARBI_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:MOTOROLA_CLIENT_SECRET::"
      },
      {
        "name": "SETTLEMENT_CLIENT_MOTOROLA_PROVIDER_METADATA_ARBI_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:MOTOROLA_CLIENT_ID::"
      },
      {
        "name": "SETTLEMENT_CLIENT_MOTOROLA_PROVIDER_METADATA_ARBI_USER_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:MOTOROLA_USER_TOKEN::"
      },
      {
        "name": "SETTLEMENT_CLIENT_GIGU_PROVIDER_METADATA_ARBI_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:GIGU_CLIENT_SECRET::"
      },
      {
        "name": "SETTLEMENT_CLIENT_GIGU_PROVIDER_METADATA_ARBI_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:GIGU_CLIENT_ID::"
      },
      {
        "name": "SETTLEMENT_CLIENT_GIGU_PROVIDER_METADATA_ARBI_USER_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:GIGU_USER_TOKEN::"
      },
      {
        "name": "SETTLEMENT_CLIENT_FRIDAY_PROVIDER_METADATA_CELCOIN_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:FRIDAY_CLIENT_ID::"
      },
      {
        "name": "SETTLEMENT_CLIENT_FRIDAY_PROVIDER_METADATA_CELCOIN_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:FRIDAY_CLIENT_SECRET::"
      },
      {
        "name": "SETTLEMENT_CLIENT_MOTOROLA_PROVIDER_METADATA_CELCOIN_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:FRIDAY_CLIENT_ID::"
      },
      {
        "name": "SETTLEMENT_CLIENT_MOTOROLA_PROVIDER_METADATA_CELCOIN_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:FRIDAY_CLIENT_SECRET::"
      },
      {
        "name": "SETTLEMENT_CLIENT_GIGU_PROVIDER_METADATA_CELCOIN_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:FRIDAY_CLIENT_ID::"
      },
      {
        "name": "SETTLEMENT_CLIENT_GIGU_PROVIDER_METADATA_CELCOIN_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:FRIDAY_CLIENT_SECRET::"
      },
      {
        "name": "AUTH_FRIDAY_IDENTITY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:settlement-service/client-credentials-4I8kwo:FRIDAY_IDENTITY::"
      },
      {
        "name": "AUTH_FRIDAY_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:settlement-service/client-credentials-4I8kwo:FRIDAY_SECRET::"
      },
      {
        "name": "AUTH_MOTOROLA_IDENTITY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:settlement-service/client-credentials-4I8kwo:MOTOROLA_IDENTITY::"
      },
      {
        "name": "AUTH_MOTOROLA_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:settlement-service/client-credentials-4I8kwo:MOTOROLA_SECRET::"
      },
      {
        "name": "AUTH_GIGU_IDENTITY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:settlement-service/client-credentials-4I8kwo:GIGU_IDENTITY::"
      },
      {
        "name": "AUTH_GIGU_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:settlement-service/client-credentials-4I8kwo:GIGU_SECRET::"
      }
    ],
    "portMappings": [
      {
        "containerPort": ${app_port},
        "hostPort": ${app_port}
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver":"awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/${service_name}",
        "auto_create_group": "false",
        "log_stream_name": "ecs/${service_name}/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 10,
    "memory": 256,
    "essential": true,
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
      ],
    "environment" : [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${service_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
  "image": "${fluent_bit_repository_url}:2.3",
  "name": "log_router",
  "essential": true,
  "cpu":0,
  "portMappings"          : [],
  "user"                  : "0",
  "volumesFrom"           : [],
  "firelensConfiguration": {
    "type": "fluentbit",
    "options": {
      "config-file-type": "file",
      "config-file-value": "/general/general.conf"
    }
  },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
  "environment" : [
    {
      "name": "SERVICE_CONTAINER_NAME",
      "value": "${container_name}"
    },
    {
      "name": "DD_ENV",
      "value": "${environment}"
    },
    {
      "name": "DD_SERVICE",
      "value": "${service_name}"
    },
    {
      "name": "LOG_GROUP_NAME",
      "value": "/logs/${service_name}"
    },
    {
      "name": "REGION",
      "value": "us-east-1"
    }
  ],
  "logConfiguration": {
    "logDriver": "awslogs",
    "options": {
      "awslogs-group": "/ecs/${service_name}-dd",
      "awslogs-region": "us-east-1",
      "awslogs-stream-prefix": "ecs"
    }
  },
  "memoryReservation": 50
}
]
