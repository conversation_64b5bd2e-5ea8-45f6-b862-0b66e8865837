locals {
  account_id                           = "************"
  production_availability_zones = ["us-east-1a", "us-east-1b"]
  incoming_emails_sns_name             = "stg-incoming-emails"
  incoming_emails_sqs_name             = "stg-incoming-emails"
  bank_account_invalidation_sqs_name   = "bank-account-invalidation"
  bill_payment_scheduling_sqs_name     = "bill_payment_scheduling"
  bill_events_sns_name                 = "bill-events-stg"
  wallet_events_sns_name               = "wallet-events-stg"
  account_events_sns_name              = "account-events-stg"
  bill_events_dlq                      = "bill_events_dlq"
  ecs_sqs_list_policy_resource = ["arn:aws:sqs:us-east-1:${local.account_id}:*"]
  user_documents_bucket_name           = "stg-user-documents"
  multicom_files_bucket_name           = "stg-multicom-files"
  facetec_bucket_name                  = "friday-facetec-documents-stg"
  modatta_user_documents_bucket_arn    = "arn:aws:s3:::************-modatta-user-documents"
  firebase_delivery_failure_queue_name = "firebase-delivery-failure-queue"
  firebase_delivery_failure_topic_name = "firebase-delivery-failure-topic"
  connect_utility_errors_bucket_arn    = "arn:aws:s3:::connect-utility-errors-stg"
  connect_utility_bills_bucket_arn     = "arn:aws:s3:::connect-utility-bills-stg"
  config_files_bucket_name             = "${local.account_id}-config-files"
  exports_bucket_name                  = "${local.account_id}-friday-stg-exports"
  prefix                               = "friday-stg"
}
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.25"
    }
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
  allowed_account_ids = [local.account_id]
}

module "backend" {
  source              = "../../modules/backend"
  environment         = var.environment
  backend_bucket_name = "stg2-backend-infrastructure"
  dynamodb_table_name = "backend-infrastructure-lock-table"
}

module "ci" {
  source      = "../../modules/ci"
  environment = var.environment
  account_id  = local.account_id
  cf_distributions = ["E3MPNJXPGW2BSC", "E1YLR5R5RPOYBU", "E1TZ5XIEAGWCZ3"]
}

module "bill-payment" {
  source                    = "../../modules/bill-payment"
  create_public_bucket      = false
  user_receipts_bucket_name = "stg-bill-receipts"
}

module "sns" {
  source              = "../../modules/sns"
  monthly_spend_limit = 20
}

terraform {
  backend "s3" {
    bucket         = "stg2-backend-infrastructure"
    key            = "stg/terraform.tfstate"
    dynamodb_table = "backend-infrastructure-lock-table"
    encrypt        = true
    region         = "us-east-1"
  }
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.1.2"
  name    = "via1-vpc"
  cidr    = "10.0.0.0/16"

  azs = ["us-east-1a", "us-east-1b"]
  public_subnets = ["********/24", "********/24"]
  private_subnets = ["**********/24", "**********/24"]

  enable_nat_gateway      = var.nat_gateway_enabled
  single_nat_gateway      = true
  enable_vpn_gateway      = false
  enable_dns_hostnames    = false
  map_public_ip_on_launch = true

  tags = {
    Terraform   = "true"
    Environment = var.environment
  }
}

module "ses-email-receiver" {
  source                                = "../../modules/ses"
  ses_receiver_rule_name                = "bill_payment_ses_receiver"
  email_domain = ["meupagador.com.br"]
  scan_enabled                          = true
  ses_bucket_name                       = "stg-ses-received-emails-via1"
  ses_incoming_emails_bucket_name       = "stg-via1-incoming-emails"
  region                                = var.aws_region
  ses_unprocessed_emails_bucket_name    = "stgses-unprocessed-emails-via1"
  rendering_errors_to_s3_bucket_enabled = true
  rendering_bucket_name                 = "email-template-rendering-errors"
  sns_receiver_emails_arn               = module.incoming_emails.topic_arn
  sns_failure_rendering_arn             = module.failure_rendering_notification.topic_arn
  quarantine_emails_bucket_name         = "stg-quarantine-emails"
  notification_email_sender             = "<EMAIL>"
}

module "incoming_emails" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.incoming_emails_sns_name
}

data "aws_iam_policy_document" "received-emails" {
  statement {
    actions = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.incoming_emails_sqs_name}"]
    principals {
      type = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values = ["arn:aws:sns:${var.aws_region}:${local.account_id}:${local.incoming_emails_sns_name}"]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

module "incoming_emails_queue" {
  source         = "terraform-aws-modules/sqs/aws"
  version        = "~> 2.0"
  policy         = data.aws_iam_policy_document.received-emails.json
  name           = local.incoming_emails_sqs_name
  redrive_policy = "{\"deadLetterTargetArn\":\"${module.incoming_emails_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":1}"

  tags = {
    Environment = var.environment
  }
}

module "incoming_emails_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "${local.incoming_emails_sqs_name}-dlq"

  tags = {
    Environment = var.environment
  }
}

resource "aws_sns_topic_subscription" "incoming_emails" {
  topic_arn            = module.incoming_emails.topic_arn
  protocol             = "sqs"
  endpoint             = module.incoming_emails_queue.this_sqs_queue_arn
  raw_message_delivery = true
}

module "failure_rendering_notification" {
  source = "terraform-aws-modules/sns/aws"
  name   = "rendering-failed-emails"
}

resource "aws_dynamodb_table" "user_table" {
  name         = "Via1-BillPayment"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  ttl {
    attribute_name = "ExpirationTTL"
    enabled        = true
  }

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex3"
    hash_key        = "GSIndex3PrimaryKey"
    range_key       = "GSIndex3ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex4"
    hash_key        = "GSIndex4PrimaryKey"
    range_key       = "GSIndex4ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "event_table" {
  name         = "Via1-BillEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "N"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "user_event_table" {
  name         = "Via1-UserEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "liveness_gateway_events_table" {
  name         = "LivenessGateway-LivenessEvent"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "ScanKey"

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "N"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
    Project     = "Liveness"
  }
}

resource "aws_dynamodb_table" "server_lock_table" {
  name         = "Shedlock"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "_id"

  attribute {
    name = "_id"
    type = "S"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_acm_certificate" "default" {
  domain_name       = "meupagador.com.br"
  subject_alternative_names = ["*.meupagador.com.br"]
  validation_method = "DNS"
  lifecycle {
    create_before_destroy = true
  }
}

module "dda_service_image_version" {
  source         = "../../modules/image_version"
  container_name = "DDABills"
  service_name   = "dda-bills-service"
}

module "bill_payment_image_version" {
  source         = "../../modules/image_version"
  container_name = "BillPaymentAPI"
  service_name   = "bill-payment-service"
}

resource "aws_ecs_cluster" "friday_fargate_cluster" {
  name = "bill-payment-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecr_repository" "fluent_bit_ecr" {
  image_tag_mutability = "IMMUTABLE"
  name                 = "custom-fluent-bit"
}

module "friday_dda_bills_task" {
  source = "../../modules/fargate_task"

  prefix                = "dda-bills"
  ecr_repository_name   = "dda-bills"
  fargate_cpu           = 256
  fargate_memory        = 1024
  task_definition       = "${path.module}/task-definitions/dda-service.json"
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.user_table.arn,
    "${aws_dynamodb_table.user_table.arn}/*",
    aws_dynamodb_table.server_lock_table.arn,
    "${aws_dynamodb_table.server_lock_table.arn}/*"
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = local.ecs_sqs_list_policy_resource
  s3_read_objects         = true
  s3_bucket_arns = ["arn:aws:s3:::************-dda-files-lambda"]
  user_pool_arn           = module.via1_cognito.user_pool_arn
  app_version             = module.dda_service_image_version.image_tag
}

resource "aws_kms_key" "connect_utility_key" {
  description             = "KMS key for bill payment encryption"
  deletion_window_in_days = 10
  multi_region            = true
}

resource "aws_kms_alias" "connect_utility_key" {
  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_key.connect_utility_key.key_id
}

provider "aws" {
  alias  = "replica"
  region = "sa-east-1"
}

resource "aws_kms_replica_key" "connect_utility_key_replica" {
  provider = aws.replica

  description             = "KMS key for bill payment encryption replica"
  deletion_window_in_days = 10
  primary_key_arn         = aws_kms_key.connect_utility_key.arn
}

resource "aws_kms_alias" "connect_utility_key_replica" {
  provider = aws.replica

  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_replica_key.connect_utility_key_replica.key_id
}

module "friday_bill_payment_task" {
  source = "../../modules/fargate_task"

  prefix                = "bill-payment"
  ecr_repository_name   = "bill-payment-api"
  fargate_cpu           = 256
  fargate_memory        = 2048
  task_definition       = "${path.module}/task-definitions/service.json"
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.user_table.arn,
    "${aws_dynamodb_table.user_table.arn}/*",
    aws_dynamodb_table.event_table.arn,
    "${aws_dynamodb_table.event_table.arn}/*",
    aws_dynamodb_table.user_event_table.arn,
    "${aws_dynamodb_table.user_event_table.arn}/*",
    aws_dynamodb_table.server_lock_table.arn,
    "${aws_dynamodb_table.server_lock_table.arn}/*",
    module.open_finance.open_finance_data_table_arn,
    "${module.open_finance.open_finance_data_table_arn}/*"
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = local.ecs_sqs_list_policy_resource
  sns_access_enabled      = true
  ecs_sns_policy_resource = [
    module.bill_events_sns.topic_arn,
    module.wallet_events_sns.topic_arn,
    module.account_events_sns.topic_arn,
    "arn:aws:sns:${var.aws_region}:${local.account_id}:*"
  ]
  s3_read_objects = true
  s3_bucket_arns = [
    "${module.ses-email-receiver.incoming_emails_bucket_arn}/*",
    "${module.ses-email-receiver.email_receiver_s3_bucket_arn}/*",
    "${module.ses-email-receiver.unprocessed_emails_bucket_arn}/*",
    "${module.ses-email-receiver.quarantine_emails_bucket_arn}/*",
    "${aws_s3_bucket.user_documents_bucket.arn}/*",
    aws_s3_bucket.multicom_files_bucket.arn,
    "${module.bill-payment.user_receipts_bucket_arn}/*",
    module.bill-payment.user_receipts_bucket_arn,
    local.modatta_user_documents_bucket_arn,
    local.connect_utility_errors_bucket_arn,
    local.connect_utility_bills_bucket_arn
  ]
  textract_enabled = true
  secrets_enabled  = true
  secrets_arns = [
    aws_secretsmanager_secret.blip_password.arn,
    aws_secretsmanager_secret.arbi_credentials.arn,
    aws_secretsmanager_secret.arbi_ecm_credentials.arn,
    aws_secretsmanager_secret.bigdatacorp_credentials.arn,
    aws_secretsmanager_secret.auth_secrets.arn,
    aws_secretsmanager_secret.userpilot.arn,
    aws_secretsmanager_secret.openai_key.arn,
    aws_secretsmanager_secret.via1_clearsale-credentials.arn,
    /* CHECK "secrets_map" attribute instead of update task definition manually */
  ]
  secrets_map   = local.bill_payment_secrets_mapping
  send_email    = true
  user_pool_arn = module.via1_cognito.user_pool_arn
  app_version   = module.bill_payment_image_version.image_tag
  kms_enabled   = true
  kms_key_arns = [aws_kms_key.connect_utility_key.arn]
}

resource "aws_security_group" "bill-payment-cache-cluster" {
  name        = "bill-payment-cache-cluster-sc"
  description = "bill-payment-cache-cluster"
  vpc_id      = module.vpc.vpc_id

  ingress {
    protocol  = "6"
    from_port = "6379"
    to_port   = "6379"
    security_groups = [
      module.bill_payment_service.ecs_tasks_security_group_id, module.dda_bills_service.ecs_tasks_security_group_id
    ]
  }
}

resource "aws_elasticache_cluster" "bill-payment-cache-cluster" {
  cluster_id           = "bill-payment-cache"
  engine               = "redis"
  node_type            = "cache.t2.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  engine_version       = "7.0"
  subnet_group_name    = aws_elasticache_subnet_group.bill-payment-cache-cluster.name
  security_group_ids = [aws_security_group.bill-payment-cache-cluster.id]

}

resource "aws_elasticache_subnet_group" "bill-payment-cache-cluster" {
  name       = "bill-payment-cache-subnet"
  subnet_ids = module.vpc.private_subnets
}

module "bill_payment_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster       = aws_ecs_cluster.friday_fargate_cluster
  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id
  prefix                = "bill-payment"
  load_balance_enabled  = true
  container_name        = "BillPaymentAPI"
  app_port              = 8443
  app_count             = 1
  health_check_path     = "/health"
  task_definition       = module.friday_bill_payment_task
  certificate_arn       = aws_acm_certificate.default.arn
}

module "dda_bills_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster       = aws_ecs_cluster.friday_fargate_cluster
  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id
  prefix                = "dda-bills"
  container_name        = "DDABills"
  app_port              = 8443
  app_count             = 1
  health_check_path     = "/health"
  task_definition       = module.friday_dda_bills_task
  certificate_arn       = aws_acm_certificate.default.arn
}

module "bill_events_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_events_dlq"
}

module "bill_payment_rollback_transaction_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  name                       = "bill_payment_rollback_transaction"
  delay_seconds              = 30
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 30
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_payment_rollback_transaction_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":144}"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_rollback_transaction_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_payment_rollback_transaction_dlq"

  tags = {
    Environment = var.environment
  }
}

module "bill_notification" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_notification"

  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_notification_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":144}"

  tags = {
    Environment = var.environment
  }
}

module "bill_notification_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_notification_dlq"

  tags = {
    Environment = var.environment
  }
}

resource "aws_acm_certificate_validation" "default" {
  certificate_arn = aws_acm_certificate.default.arn
  validation_record_fqdns = [
    "_cf915d1fa141eb68a9c9af748080e9fd.meupagador.com.br"
  ]
}
resource "aws_secretsmanager_secret" "datadog_key" {
  name = "bill-payment-api/datadog"
}

resource "aws_secretsmanager_secret" "blip_password" {
  name = "bill-payment-api/blip-password"
}

resource "aws_secretsmanager_secret" "arbi_credentials" {
  name = "bill-payment-api/arbi-credentials"
}

resource "aws_secretsmanager_secret" "userpilot" {
  name = "bill-payment-api/userpilot"
}

resource "aws_secretsmanager_secret" "openai_key" {
  name = "friday/openai_key"
}

resource "aws_secretsmanager_secret" "openai_daily_log_key" {
  name = "friday/openai_daily_log_key"
}

# Já foram inicializadas. Descomentar em caso de novo ambiente
# resource "aws_secretsmanager_secret_version" "openai_daily_log_key" {
#   secret_id = aws_secretsmanager_secret.openai_daily_log_key.id
#   secret_string = ""
# }

resource "aws_secretsmanager_secret" "waba_token" {
  name = "friday/waba-token"
}

# Já foram inicializadas
# resource "aws_secretsmanager_secret_version" "waba_token" {
#   secret_id = aws_secretsmanager_secret.waba_token.id
#   secret_string = ""
# }

resource "aws_secretsmanager_secret" "arbi_ecm_credentials" {
  name = "bill-payment-api/arbi-ecm-credentials"
}

resource "aws_secretsmanager_secret" "auth_secrets" {
  name = "bill-payment-api/auth_secrets"
}

resource "aws_secretsmanager_secret" "bigdatacorp_credentials" {
  name = "via1/bigdatacorp-credentials"
}

resource "aws_secretsmanager_secret" "liveness_gateway_secrets" {
  name = "liveness-gateway/facetec"
  tags = {
    Project = "Liveness"
  }
}

resource "aws_secretsmanager_secret" "via1_clearsale-credentials" {
  name = "friday/clearsale-credentials"
}

resource "aws_secretsmanager_secret_version" "via1_clearsale-credentials-initialization" {
  secret_id = aws_secretsmanager_secret.via1_clearsale-credentials.id
  secret_string = jsonencode({
    "HOST"     = "REPLACE_HOST"
    "USERNAME" = "REPLACE_USERNAME"
    "PASSWORD" = "REPLACE_PASSWORD"
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}

data "aws_iam_policy_document" "bank_account_policy" {
  statement {
    actions = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.bank_account_invalidation_sqs_name}"]
    principals {
      type = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values = [module.bill_events_sns.topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

data "aws_iam_policy_document" "bill_payment_scheduling_queue_policy" {
  statement {
    actions = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.bill_payment_scheduling_sqs_name}"]
    principals {
      type = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values = [module.bill_events_sns.topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

resource "aws_sns_topic_subscription" "bank_account_invalidation_queue_subscription" {
  topic_arn            = module.bill_events_sns.topic_arn
  protocol             = "sqs"
  endpoint             = module.bank_account_invalidation_queue.this_sqs_queue_arn
  filter_policy = jsonencode({ "eventType" = tolist(["PAYMENT_FAIL", "PAYMENT_REFUNDED"]) })
  raw_message_delivery = true
}

resource "aws_sns_topic_subscription" "bill_payment_scheduling_queue_subscription" {
  topic_arn = module.bill_events_sns.topic_arn
  protocol  = "sqs"
  endpoint  = module.bill_payment_scheduling_queue.this_sqs_queue_arn
  filter_policy = jsonencode({
    "eventType" = tolist(["AMOUNT_UPDATED", "PAYMENT_SCHEDULED", "PAYMENT_SCHEDULE_CANCELED", "REGISTER_UPDATED"])
  })
  raw_message_delivery = true
}

module "bill_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.bill_events_sns_name
}

module "wallet_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.wallet_events_sns_name
}

module "account_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.account_events_sns_name
}

module "bank_account_invalidation_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.bank_account_policy.json
  name                       = local.bank_account_invalidation_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bank_account_invalidation_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":60}"

  tags = {
    Environment = var.environment
  }
}

module "bank_account_invalidation_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bank-account-invalidation-dlq"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_scheduling_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.bill_payment_scheduling_queue_policy.json
  name                       = local.bill_payment_scheduling_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_payment_scheduling_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":60}"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_scheduling_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "${local.bill_payment_scheduling_sqs_name}_dlq"

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "user_documents_bucket" {
  bucket = local.user_documents_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = false
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "multicom_files_bucket" {
  bucket = local.multicom_files_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = false
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "facetec-bucket" {
  bucket = local.facetec_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = false
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

module "via1_cognito" {
  source                           = "../../modules/cognito"
  user_pool_name                   = "stg-via1-user-pool"
  user_pool_domain_name            = "auth.meupagador.com.br"
  user_pool_domain_certificate_arn = aws_acm_certificate.default.arn
  user_pool_domain_zone_id         = "Z313GN63VRR695"
  provider_google_app_id           = "681648395244-dvjuq2n41cjvau4shvgclrd10j0ponv9.apps.googleusercontent.com"
  provider_google_app_secret       = "0T749D1uSwgertRw7-b8CJcr"
  notification_email_sender_arn    = module.ses-email-receiver.notification_email_sender_arn
  provider_apple_client_id         = "br.com.meupagador.webapp"
  provider_apple_team_id           = "AJ6L79GVKW"
  provider_apple_key_id            = "GVC7L6KRAR"
  provider_apple_private_key       = "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgZU1SLkSicFkaQ/43Am03Fnqj8+CfulUzB+qP6IHcfKGgCgYIKoZIzj0DAQehRANCAAS8a/Pj+yu1JmhASwv9OPbvjX8KxgZHmkKU+MTDzUjJXe7sPtQRs/oHnmP1PJ4Wg/LSG5WUy8/lqunVwywd/M7u"
  id_token_validity                = 60
  access_token_validity            = 60
  callback_urls = [
    "https://use.local.via1.io:20444/autenticacao-federado", "https://use.meupagador.com.br/autenticacao-federado"
  ]
  logout_urls = ["https://use.local.via1.io:20444/", "https://use.meupagador.com.br/"]
  prefix = local.prefix
  environment = var.environment
  datadog_key_arn = aws_secretsmanager_secret.datadog_key.arn
  lambda_config_enabled = true
}

data "aws_iam_policy_document" "docker_runner_policy_document" {
  statement {
    actions = [
      "iam:PassRole",
      "ecs:UpdateService",
      "ecs:RegisterTaskDefinition",
      "ecs:DescribeTasks",
      "ecs:DescribeTaskDefinition",
      "ecs:StartTask",
      "ecr:UploadLayerPart",
      "ecr:PutImage",
      "ecr:ListImages",
      "ecr:InitiateLayerUpload",
      "ecr:GetDownloadUrlForLayer",
      "ecr:GetAuthorizationToken",
      "ecr:DescribeRepositories",
      "ecr:DescribeImages",
      "ecr:CompleteLayerUpload",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
      "s3:GetObject",
      "s3:GetObjectTagging",
      "s3:PutObject",
      "s3:PutObjectTagging",
      "s3:ListBucket"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "docker_runner_policy" {
  name   = "PicpayDockerMachinePolicy"
  path   = "/"
  policy = data.aws_iam_policy_document.docker_runner_policy_document.json
}

module "runner" {
  source      = "cattle-ops/gitlab-runner/aws"
  environment = "friday-stg"
  vpc_id      = module.vpc.vpc_id
  subnet_id = element(module.vpc.private_subnets, 0)
  runner_worker_docker_machine_ec2_options = [
    "engine-install-url='https://releases.rancher.com/install-docker/24.0.2.sh'"
  ]
  runner_worker_docker_add_dind_volumes = false
  runner_worker_docker_volumes_tmpfs = [
    {
      volume  = "/var/opt/cache",
      options = "rw,noexec"
    }
  ]
  runner_worker_docker_machine_fleet = {
    enable = true
  }
  runner_worker_docker_services_volumes_tmpfs = [
    {
      volume  = "/var/lib/mysql",
      options = "rw,noexec"
    }
  ]
  runner_gitlab_registration_config = {
    registration_token = "GR1348941xFhYoVzLAnd5WzNGSqCs"
    tag_list = "friday" // this tag used in CICD
    description        = "FRIDAY"
    locked_to_project  = "true"
    run_untagged       = "false"
    maximum_timeout    = "3600"
  }
  runner_worker_docker_options = {
    tls_verify                   = false
    image                        = "docker:latest"
    privileged                   = false
    disable_entrypoint_overwrite = false
    oom_kill_disable             = false
    disable_cache                = false
    volumes = ["/cache", "/var/run/docker.sock:/var/run/docker.sock", "/tmp/builds:/tmp/builds"]
    shm_size                     = 0
    pull_policies = ["if-not-present"]
  }
  runner_instance = {
    name       = "docker-default"
    ssm_access = true
    root_device_config = {
      volume_size = 200
    }
  }
  runner_install = {
    post_install_script = ""
  }
  runner_gitlab = {
    url            = "https://gitlab.com"
    runner_version = "16.2.0"
  }
  runner_worker = {
    ssm_access = true
  }
  runner_worker_cache = {
    shared = true
  }
  runner_worker_docker_machine_instance = {
    root_size  = 210
    types = ["c7a.12xlarge"]
    root_size  = 210
    subnet_ids = module.vpc.private_subnets
  }
  runner_worker_docker_machine_instance_spot = {
    enable = false
  }
  runner_worker_docker_machine_role = {
    policy_arns = [aws_iam_policy.docker_runner_policy.arn]
  }
}

resource "aws_s3_bucket" "fluent_bit_configs" {
  bucket = local.config_files_bucket_name
}

resource "aws_s3_object" "fluent_bit_general_conf" {
  bucket = aws_s3_bucket.fluent_bit_configs.bucket
  key    = "config/general.conf"
  source = "../../files/fluent-bit/general.conf"
  acl    = "private"
}

resource "aws_s3_object" "fluent_bit_parsers_conf" {
  bucket = aws_s3_bucket.fluent_bit_configs.bucket
  key    = "config/parsers.conf"
  source = "../../files/fluent-bit/parsers.conf"
  acl    = "private"
}

module "debitos_veiculares" {
  source          = "../../modules/debitos-veiculares"
  environment     = var.environment
  cluster         = aws_ecs_cluster.friday_fargate_cluster
  aws_region      = var.aws_region
  account_id      = local.account_id
  vpc_id          = module.vpc.vpc_id
  private_subnets = module.vpc.private_subnets
  public_subnets  = module.vpc.public_subnets
  fargate_cpu     = 1024
  fargate_memory  = 2048
  certificate_arn = aws_acm_certificate.default.arn
  s3_bucket_arns = [
    aws_s3_bucket.fluent_bit_configs.arn
  ]
  secrets = [
    aws_secretsmanager_secret.datadog_key.arn,
  ]
}

module "ecs_liveness" {
  source = "../../modules/liveness"

  environment           = var.environment
  ecr_repository_name   = "liveness-api"
  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id
  cluster_name          = "liveness-cluster"
  service_name          = "liveness-service"
  container_name        = "LivenessAPI"
  task_name             = "liveness-gateway-task"
  alb_target_group_name = "liveness-target-group"
  load_balance_name     = "liveness-alb"
  app_port              = 8443
  app_count             = 1
  fargate_cpu           = 512
  fargate_memory        = 2048
  health_check_path     = "/health"
  task_definition       = "${path.module}/task-definitions/liveness-gateway.json"
  certificate_arn       = aws_acm_certificate.default.arn
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.liveness_gateway_events_table.arn,
    "${aws_dynamodb_table.liveness_gateway_events_table.arn}/*",
  ]
  ecs_task_execution_role_name = "liveness-task-execution-role"
  ecs_task_role_name           = "liveness-task-role"
  s3_read_objects              = true
  s3_bucket_arns = [aws_s3_bucket.facetec-bucket.arn]
  secrets_enabled              = true
  secrets_arns = [
    aws_secretsmanager_secret.datadog_key.arn,
    aws_secretsmanager_secret.liveness_gateway_secrets.arn,
  ]
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  sqs_access_enabled        = true
  ecs_sqs_policy_resource   = local.ecs_sqs_list_policy_resource
  tags = {
    Project = "Liveness"
  }
  usage_logs_enabled = false
  use_fargate = false
}

module "ecs_settlement-service" {
  source = "../../modules/settlement-service"

  environment                  = var.environment
  ecr_repository_name          = "settlement-service-api"
  aws_private_subnet_id        = module.vpc.private_subnets
  aws_public_subnet_id         = module.vpc.public_subnets
  aws_vpc_id                   = module.vpc.vpc_id
  cluster_name                 = "settlement-service-cluster"
  service_name                 = "settlement-service"
  container_name               = "settlement-serviceAPI"
  task_name                    = "settlement-service-task"
  alb_target_group_name        = "settlement-service-target-group"
  load_balance_name            = "settlement-service-alb"
  app_port                     = 8443
  app_count                    = 1
  fargate_cpu                  = 512
  fargate_memory               = 2048
  health_check_path            = "/health"
  task_definition              = "${path.module}/task-definitions/settlement-service.json"
  certificate_arn              = aws_acm_certificate.default.arn
  dynamo_access_enabled        = true
  ecs_task_execution_role_name = "settlement-service-task-execution-role"
  ecs_task_role_name           = "settlement-service-task-role"
  s3_read_objects              = false
  s3_bucket_arns = []
  secrets_arns = [
    aws_secretsmanager_secret.datadog_key.arn
  ]
  fluent_bit_repository_url  = aws_ecr_repository.fluent_bit_ecr.repository_url
  sqs_access_enabled         = true
  ecs_sqs_policy_resource    = local.ecs_sqs_list_policy_resource
  settlement_events_sns_name = "settlement-events-stg"
  tags = {
    Project = "settlement-service"
  }
}


module "firebase_sns_platform_application" {
  source                      = "../../modules/sns-platform-application"
  delivery_failure_queue_arn  = "arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.firebase_delivery_failure_queue_name}"
  delivery_failure_queue_name = local.firebase_delivery_failure_queue_name
  delivery_failure_topic_name = local.firebase_delivery_failure_topic_name
  sns_platform_application    = "firebase_sns_platform_application"
}

resource "aws_s3_bucket" "exports" {
  bucket = local.exports_bucket_name
}

module "ai_chatbot" {
  source = "../../modules/ai-chatbot"

  aws_region                   = "us-east-1"
  environment                  = var.environment
  task_definition              = "${path.module}/task-definitions/ai-chatbot.json"
  fluent_bit_repository_url    = aws_ecr_repository.fluent_bit_ecr.repository_url
  private_subnets              = module.vpc.private_subnets
  public_subnets               = module.vpc.public_subnets
  vpc_id                       = module.vpc.vpc_id
  certificate_arn              = aws_acm_certificate.default.arn
  ecs_sqs_list_policy_resource = local.ecs_sqs_list_policy_resource
  shedlock_table_arn           = aws_dynamodb_table.server_lock_table.arn
  billpayment_table_arn        = aws_dynamodb_table.user_table.arn
  secrets_arns = {
    "OPENAI_TOKEN"                               = aws_secretsmanager_secret.openai_key.arn,
    "OPENAI_DAILY_LOG_TOKEN"                     = aws_secretsmanager_secret.openai_daily_log_key.arn,
    "INTEGRATIONS_WHATSAPP_API_TOKEN"            = aws_secretsmanager_secret.waba_token.arn,
    "COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH" = aws_secretsmanager_secret.blip_password.arn,
  }
  s3_bucket_arns = [module.wa_comm_centre.wa_comm_centre_bucket_arn, aws_s3_bucket.exports.arn]
  cognito_token_sns_topic_arn = module.via1_cognito.cognito_token_sns_topic_arn
  cognito_custom_sender_kms_key_arn = module.via1_cognito.cognito_custom_sender_kms_key_arn
}

module "wa_comm_centre" {
  source = "../../modules/wa-comm-centre"

  aws_region                = "us-east-1"
  environment               = var.environment
  task_definition           = "${path.module}/task-definitions/wa-comm-centre.json"
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  private_subnets           = module.vpc.private_subnets
  public_subnets            = module.vpc.public_subnets
  vpc_id                    = module.vpc.vpc_id
  certificate_arn           = aws_acm_certificate.default.arn
  shedlock_table_arn        = aws_dynamodb_table.server_lock_table.arn
  ecs_sqs_policy_resource   = local.ecs_sqs_list_policy_resource
  wa_comm_centre_bucket_name = "${local.account_id}-wa-comm-centre"
  mtls_certificate_bucket = "NO_BUCKET"
  mtls_certificate_key = "NO_KEY"
}

module "open_finance" {
  source = "../../modules/open-finance"

  aws_region                = "us-east-1"
  environment               = var.environment
  task_definition           = "${path.module}/task-definitions/open-finance.json"
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  private_subnets           = module.vpc.private_subnets
  public_subnets            = module.vpc.public_subnets
  vpc_id                    = module.vpc.vpc_id
  certificate_arn           = aws_acm_certificate.default.arn
  client_task_role_arn      = module.friday_bill_payment_task.task_role_arn
  account_id                = local.account_id
}

resource "aws_vpc_endpoint" "ecr_api" {
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.aws_region}.ecr.api"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = false

  security_group_ids = [
    module.open_finance.alb_security_group_id,
    module.bill_payment_service.alb_security_group_id,
    module.ecs_settlement-service.alb_security_group_id,
    module.dda_bills_service.alb_security_group_id,
    module.ecs_liveness.alb_security_group_id
  ]
  subnet_ids = module.vpc.private_subnets
}

resource "aws_vpc_endpoint" "s3" {
  vpc_id            = module.vpc.vpc_id
  service_name      = "com.amazonaws.${var.aws_region}.s3"
  vpc_endpoint_type = "Gateway"

  route_table_ids = module.vpc.private_route_table_ids
}